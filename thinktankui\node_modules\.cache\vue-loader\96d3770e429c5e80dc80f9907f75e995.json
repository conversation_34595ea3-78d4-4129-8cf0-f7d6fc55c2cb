{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=template&id=040a21b8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751527704653}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750933731210}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}