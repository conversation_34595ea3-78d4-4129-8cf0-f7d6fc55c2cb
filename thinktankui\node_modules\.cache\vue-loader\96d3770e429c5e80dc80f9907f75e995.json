{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=template&id=040a21b8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751527395861}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750933731210}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}