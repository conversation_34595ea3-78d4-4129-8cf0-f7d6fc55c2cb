{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=template&id=040a21b8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751525369916}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750933731210}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}