{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751527395861}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAiLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n        <span class=\"step-number\">1</span>\n        <span class=\"step-text\">舆情分析来源</span>\n      </div>\n      <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n        <span class=\"step-number\">2</span>\n        <span class=\"step-text\">数据概览</span>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <span class=\"section-label\">选择关联词</span>\n            <span class=\"word-count\">(0/5)</span>\n          </div>\n\n          <div class=\"keywords-textbox-wrapper\">\n            <!-- 显示生成的关键词 -->\n            <div v-if=\"selectedKeywords.length > 0\" class=\"generated-keywords-display\">\n              <div v-for=\"(category, categoryName) in groupedKeywords\" :key=\"categoryName\" class=\"keyword-category\">\n                <div class=\"category-label\">{{ categoryName }}</div>\n                <div class=\"keyword-tags\">\n                  <el-tag\n                    v-for=\"(keyword, index) in category\"\n                    :key=\"index\"\n                    :class=\"['keyword-tag', { selected: isKeywordSelected(keyword) }]\"\n                    @click=\"toggleKeyword(keyword)\"\n                  >\n                    {{ keyword }}\n                  </el-tag>\n                </div>\n              </div>\n            </div>\n\n            <!-- 生成关联词按钮区域 -->\n            <div class=\"words-container\" :class=\"{ 'has-keywords': selectedKeywords.length > 0 }\">\n              <div class=\"generate-word-btn\" @click=\"generateRelatedWords\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>生成关联词</span>\n              </div>\n              <div class=\"word-description\">\n                根据你填写的需求和关键词生成关联词\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\" @click=\"toggleDataSource('online-search')\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"'online-search'\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 自定义数据源列表 -->\n          <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-option\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"source\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>{{ source }}</h3>\n            </div>\n            <div class=\"source-actions\">\n              <i class=\"el-icon-delete\" @click=\"removeCustomSource(index)\"></i>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n\n\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [], // 已选择的关键词\n      maxKeywords: 5, // 最大选择数量\n      selectedDataSources: ['online-search'], // 已选择的数据来源\n      customDataSources: [], // 自定义数据源列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      showValidation: false // 是否显示验证错误样式\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    },\n\n    // 将关键词按分类分组\n    groupedKeywords() {\n      if (this.selectedKeywords.length === 0) {\n        return {}\n      }\n\n      const grouped = {}\n\n      this.selectedKeywords.forEach(keyword => {\n        let category = '其他'\n\n        if (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服')) {\n          category = '售后服务问题'\n        } else if (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障')) {\n          category = '产品质量问题'\n        } else if (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解')) {\n          category = '投诉处理结果'\n        } else if (keyword.includes('不满') || keyword.includes('消费者')) {\n          category = '消费者不满'\n        } else if (keyword.includes('宣传') || keyword.includes('充好')) {\n          category = '虚假宣传'\n        }\n\n        if (!grouped[category]) {\n          grouped[category] = []\n        }\n        grouped[category].push(keyword)\n      })\n\n      return grouped\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 切换数据来源选择\n    toggleDataSource(source) {\n      const index = this.selectedDataSources.indexOf(source)\n      if (index > -1) {\n        this.selectedDataSources.splice(index, 1)\n      } else {\n        this.selectedDataSources.push(source)\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 将新的数据源添加到自定义数据源列表中\n      this.customDataSources.push(trimmedUrl)\n      // 自动选中新添加的数据源\n      this.selectedDataSources.push(trimmedUrl)\n\n      this.$message.success('数据源添加成功')\n      // 清空输入框，但保持表单显示，允许继续添加\n      this.newSourceUrl = ''\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      const sourceToRemove = this.customDataSources[index]\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n      // 从已选择列表中移除\n      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)\n      if (selectedIndex > -1) {\n        this.selectedDataSources.splice(selectedIndex, 1)\n      }\n      this.$message.success('数据源删除成功')\n    },\n\n    // 生成关联词\n    generateRelatedWords() {\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      // 这里可以调用API生成关联词\n      this.$message.info('正在生成关联词...')\n\n      // 模拟生成关联词的过程\n      setTimeout(() => {\n        // 根据实体关键词生成相关的关联词\n        const generatedWords = [\n          '老板电器 售后服务',\n          '老板电器 三包义务',\n          '老板电器 客服态度',\n          '老板电器 质量',\n          '老板电器 燃气灶爆炸',\n          '老板电器 抽油烟机故障',\n          '老板电器 投诉处理',\n          '老板电器 对解',\n          '老板电器 投诉公示',\n          '老板电器 消费者不满',\n          '老板电器 不满',\n          '老板电器 投诉平台',\n          '老板电器 虚假宣传',\n          '老板电器 以次充好'\n        ]\n\n        // 清空之前的关键词，添加新生成的关联词\n        this.selectedKeywords = []\n        generatedWords.forEach(word => {\n          if (this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(word)\n          }\n        })\n\n        this.$message.success('关联词生成成功')\n      }, 1000)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 0;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: center;\n  gap: 60px;\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n\n    .keyword-count {\n      color: #999;\n      font-weight: normal;\n      margin-left: 8px;\n      font-size: 13px;\n\n      &.max-reached {\n        color: #ff4d4f;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-textarea__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-bottom: 16px;\n\n    .section-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .word-count {\n      font-size: 14px;\n      color: #999;\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词文本框包装器\n.keywords-textbox-wrapper {\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  padding: 16px;\n  background: #fff;\n  min-height: 120px;\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    border-color: #5470c6;\n  }\n\n  &:focus-within {\n    border-color: #5470c6;\n    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n  }\n}\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    gap: 30px;\n    padding: 16px 0;\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n</style>\n"]}]}