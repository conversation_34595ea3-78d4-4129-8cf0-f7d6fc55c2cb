{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751525369916}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\babel.config.js", "mtime": 1750933247176}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRlc3QuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnRyaW0uanMiKTsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdPcGluaW9uQW5hbHlzaXMnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjdXJyZW50U3RlcDogMSwKICAgICAgLy8g5b2T5YmN5q2l6aqkCiAgICAgIGVudGl0eUtleXdvcmQ6ICcnLAogICAgICAvLyDlrp7kvZPlhbPplK7or40KICAgICAgc3BlY2lmaWNSZXF1aXJlbWVudDogJycsCiAgICAgIC8vIOWFt+S9k+mcg<PERSON>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"}, {"version": 3, "names": ["name", "data", "currentStep", "entityKeyword", "specificRequirement", "selectedKeywords", "maxKeywords", "selectedDataSources", "customDataSources", "showAddSourceInput", "newSourceUrl", "showValidation", "computed", "canGoToNextStep", "trim", "length", "mounted", "console", "log", "methods", "toggleKeyword", "keyword", "index", "indexOf", "splice", "push", "$message", "warning", "concat", "isKeywordSelected", "includes", "goToNextStep", "goToPreviousStep", "toggleDataSource", "source", "showAddSourceForm", "hideAddSourceForm", "confirmAddSource", "urlPattern", "test", "trimmedUrl", "success", "removeCustomSource", "sourceToRemove", "selectedIndex"], "sources": ["src/views/opinion-analysis/index.vue"], "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n        <span class=\"step-number\">1</span>\n        <span class=\"step-text\">舆情分析来源</span>\n      </div>\n      <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n        <span class=\"step-number\">2</span>\n        <span class=\"step-text\">数据概览</span>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <!--\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <span class=\"section-label\">选择关联词</span>\n            <span class=\"word-count\">(0/5)</span>\n          </div>\n\n          <div class=\"words-container\">\n            <div class=\"generate-word-btn\">\n              <i class=\"el-icon-magic-stick\"></i>\n              <span>生成关联词</span>\n            </div>\n            <div class=\"word-description\">\n              根据你填写的需求和关键词生成关联词\n            </div>\n          </div>\n        </div>\n        -->\n\n        <!-- 新的关键词选择区域 -->\n        <div class=\"keywords-selection-section\">\n          <div class=\"keywords-grid\">\n            <!-- 业绩下滑 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">业绩下滑</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 业绩下滑') }]\"\n                  @click=\"toggleKeyword('老板电器 业绩下滑')\"\n                >老板电器 业绩下滑</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 营收下降') }]\"\n                  @click=\"toggleKeyword('老板电器 营收下降')\"\n                >老板电器 营收下降</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 净利润下降') }]\"\n                  @click=\"toggleKeyword('老板电器 净利润下降')\"\n                >老板电器 净利润下降</el-tag>\n              </div>\n            </div>\n\n            <!-- 质量问题 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">质量问题</div>\n              <div class=\"keyword-tags\">\n                   <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('产品质量') }]\"\n                  @click=\"toggleKeyword('产品质量')\"\n                >产品质量</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 爆炸门') }]\"\n                  @click=\"toggleKeyword('老板电器 爆炸门')\"\n                >老板电器 爆炸门</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 投诉') }]\"\n                  @click=\"toggleKeyword('老板电器 投诉')\"\n                >老板电器 投诉</el-tag>\n              </div>\n            </div>\n\n            <!-- 股价下跌 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">股价下跌</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 股价下跌') }]\"\n                  @click=\"toggleKeyword('老板电器 股价下跌')\"\n                >老板电器 股价下跌</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 市值缩水') }]\"\n                  @click=\"toggleKeyword('老板电器 市值缩水')\"\n                >老板电器 市值缩水</el-tag>\n              </div>\n            </div>\n\n            <!-- 子公司亏损 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">子公司亏损</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 子公司亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 子公司亏损')\"\n                >老板电器 子公司亏损</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 名气亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 名气亏损')\"\n                >老板电器 名气亏损</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 金帝亏损') }]\"\n                  @click=\"toggleKeyword('老板电器 金帝亏损')\"\n                >老板电器 金帝亏损</el-tag>\n              </div>\n            </div>\n\n            <!-- 渠道问题 -->\n            <div class=\"keyword-category\">\n              <div class=\"category-label\">渠道问题</div>\n              <div class=\"keyword-tags\">\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 渠道冲突') }]\"\n                  @click=\"toggleKeyword('老板电器 渠道冲突')\"\n                >老板电器 渠道冲突</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 串货问题') }]\"\n                  @click=\"toggleKeyword('老板电器 串货问题')\"\n                >老板电器 串货问题</el-tag>\n                <el-tag\n                  :class=\"['keyword-tag', { selected: isKeywordSelected('老板电器 经销商压力') }]\"\n                  @click=\"toggleKeyword('老板电器 经销商压力')\"\n                >老板电器 经销商压力</el-tag>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\" @click=\"toggleDataSource('online-search')\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"'online-search'\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 自定义数据源列表 -->\n          <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-option\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"source\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>{{ source }}</h3>\n            </div>\n            <div class=\"source-actions\">\n              <i class=\"el-icon-delete\" @click=\"removeCustomSource(index)\"></i>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n\n\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [\n        '老板电器 业绩下滑',\n        '老板电器 营收下降',\n        '老板电器 净利润下降',\n        '老板电器 爆炸门'\n      ], // 已选择的关键词\n      maxKeywords: 5, // 最大选择数量\n      selectedDataSources: ['online-search'], // 已选择的数据来源\n      customDataSources: [], // 自定义数据源列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      showValidation: false // 是否显示验证错误样式\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 切换数据来源选择\n    toggleDataSource(source) {\n      const index = this.selectedDataSources.indexOf(source)\n      if (index > -1) {\n        this.selectedDataSources.splice(index, 1)\n      } else {\n        this.selectedDataSources.push(source)\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 将新的数据源添加到自定义数据源列表中\n      this.customDataSources.push(trimmedUrl)\n      // 自动选中新添加的数据源\n      this.selectedDataSources.push(trimmedUrl)\n\n      this.$message.success('数据源添加成功')\n      // 清空输入框，但保持表单显示，允许继续添加\n      this.newSourceUrl = ''\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      const sourceToRemove = this.customDataSources[index]\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n      // 从已选择列表中移除\n      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)\n      if (selectedIndex > -1) {\n        this.selectedDataSources.splice(selectedIndex, 1)\n      }\n      this.$message.success('数据源删除成功')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 0;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: center;\n  gap: 60px;\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-textarea__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-bottom: 16px;\n\n    .section-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .word-count {\n      font-size: 14px;\n      color: #999;\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    gap: 30px;\n    padding: 16px 0;\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;iCA+PA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MAAA;MACAC,aAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,gBAAA,GACA,aACA,aACA,cACA,WACA;MAAA;MACAC,WAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA;MACA,UAAAV,aAAA,CAAAW,IAAA;QACA;MACA;;MAEA;MACA,UAAAV,mBAAA,CAAAU,IAAA;QACA;MACA;;MAEA;MACA,SAAAT,gBAAA,CAAAU,MAAA;QACA;MACA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACAC,OAAA,CAAAC,GAAA;EACA;EACAC,OAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,OAAA;MACA,IAAAC,KAAA,QAAAjB,gBAAA,CAAAkB,OAAA,CAAAF,OAAA;MACA,IAAAC,KAAA;QACA;QACA,KAAAjB,gBAAA,CAAAmB,MAAA,CAAAF,KAAA;MACA;QACA;QACA,SAAAjB,gBAAA,CAAAU,MAAA,QAAAT,WAAA;UACA,KAAAD,gBAAA,CAAAoB,IAAA,CAAAJ,OAAA;QACA;UACA,KAAAK,QAAA,CAAAC,OAAA,wCAAAC,MAAA,MAAAtB,WAAA;QACA;MACA;IACA;IAEA;IACAuB,iBAAA,WAAAA,kBAAAR,OAAA;MACA,YAAAhB,gBAAA,CAAAyB,QAAA,CAAAT,OAAA;IACA;IAEA;IACAU,YAAA,WAAAA,aAAA;MACA;MACA,KAAApB,cAAA;;MAEA;MACA,UAAAR,aAAA,CAAAW,IAAA;QACA,KAAAY,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,UAAAvB,mBAAA,CAAAU,IAAA;QACA,KAAAY,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,SAAAtB,gBAAA,CAAAU,MAAA;QACA,KAAAW,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAhB,cAAA;MACA,SAAAT,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACA8B,gBAAA,WAAAA,iBAAA;MACA,SAAA9B,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACA+B,gBAAA,WAAAA,iBAAAC,MAAA;MACA,IAAAZ,KAAA,QAAAf,mBAAA,CAAAgB,OAAA,CAAAW,MAAA;MACA,IAAAZ,KAAA;QACA,KAAAf,mBAAA,CAAAiB,MAAA,CAAAF,KAAA;MACA;QACA,KAAAf,mBAAA,CAAAkB,IAAA,CAAAS,MAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAA1B,kBAAA;MACA,KAAAC,YAAA;IACA;IAEA;IACA0B,iBAAA,WAAAA,kBAAA;MACA,KAAA3B,kBAAA;MACA,KAAAC,YAAA;IACA;IAEA;IACA2B,gBAAA,WAAAA,iBAAA;MACA,UAAA3B,YAAA,CAAAI,IAAA;QACA,KAAAY,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAW,UAAA;MACA,KAAAA,UAAA,CAAAC,IAAA,MAAA7B,YAAA,CAAAI,IAAA;QACA,KAAAY,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAa,UAAA,QAAA9B,YAAA,CAAAI,IAAA;MACA,SAAAN,iBAAA,CAAAsB,QAAA,CAAAU,UAAA;QACA,KAAAd,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAnB,iBAAA,CAAAiB,IAAA,CAAAe,UAAA;MACA;MACA,KAAAjC,mBAAA,CAAAkB,IAAA,CAAAe,UAAA;MAEA,KAAAd,QAAA,CAAAe,OAAA;MACA;MACA,KAAA/B,YAAA;IACA;IAEA;IACAgC,kBAAA,WAAAA,mBAAApB,KAAA;MACA,IAAAqB,cAAA,QAAAnC,iBAAA,CAAAc,KAAA;MACA;MACA,KAAAd,iBAAA,CAAAgB,MAAA,CAAAF,KAAA;MACA;MACA,IAAAsB,aAAA,QAAArC,mBAAA,CAAAgB,OAAA,CAAAoB,cAAA;MACA,IAAAC,aAAA;QACA,KAAArC,mBAAA,CAAAiB,MAAA,CAAAoB,aAAA;MACA;MACA,KAAAlB,QAAA,CAAAe,OAAA;IACA;EACA;AACA", "ignoreList": []}]}