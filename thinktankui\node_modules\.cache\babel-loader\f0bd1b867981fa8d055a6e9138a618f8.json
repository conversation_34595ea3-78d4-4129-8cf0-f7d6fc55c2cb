{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751527395861}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\babel.config.js", "mtime": 1750933247176}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "currentStep", "entityKeyword", "specificRequirement", "selectedKeywords", "maxKeywords", "selectedDataSources", "customDataSources", "showAddSourceInput", "newSourceUrl", "showValidation", "computed", "canGoToNextStep", "trim", "length", "groupedKeywords", "grouped", "for<PERSON>ach", "keyword", "category", "includes", "push", "mounted", "console", "log", "methods", "toggleKeyword", "index", "indexOf", "splice", "$message", "warning", "concat", "isKeywordSelected", "goToNextStep", "goToPreviousStep", "toggleDataSource", "source", "showAddSourceForm", "hideAddSourceForm", "confirmAddSource", "urlPattern", "test", "trimmedUrl", "success", "removeCustomSource", "sourceToRemove", "selectedIndex", "generateRelatedWords", "_this", "info", "setTimeout", "generatedWords", "word"], "sources": ["src/views/opinion-analysis/index.vue"], "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n        <span class=\"step-number\">1</span>\n        <span class=\"step-text\">舆情分析来源</span>\n      </div>\n      <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n        <span class=\"step-number\">2</span>\n        <span class=\"step-text\">数据概览</span>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <span class=\"section-label\">选择关联词</span>\n            <span class=\"word-count\">(0/5)</span>\n          </div>\n\n          <div class=\"keywords-textbox-wrapper\">\n            <!-- 显示生成的关键词 -->\n            <div v-if=\"selectedKeywords.length > 0\" class=\"generated-keywords-display\">\n              <div v-for=\"(category, categoryName) in groupedKeywords\" :key=\"categoryName\" class=\"keyword-category\">\n                <div class=\"category-label\">{{ categoryName }}</div>\n                <div class=\"keyword-tags\">\n                  <el-tag\n                    v-for=\"(keyword, index) in category\"\n                    :key=\"index\"\n                    :class=\"['keyword-tag', { selected: isKeywordSelected(keyword) }]\"\n                    @click=\"toggleKeyword(keyword)\"\n                  >\n                    {{ keyword }}\n                  </el-tag>\n                </div>\n              </div>\n            </div>\n\n            <!-- 生成关联词按钮区域 -->\n            <div class=\"words-container\" :class=\"{ 'has-keywords': selectedKeywords.length > 0 }\">\n              <div class=\"generate-word-btn\" @click=\"generateRelatedWords\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>生成关联词</span>\n              </div>\n              <div class=\"word-description\">\n                根据你填写的需求和关键词生成关联词\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\" @click=\"toggleDataSource('online-search')\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"'online-search'\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 自定义数据源列表 -->\n          <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-option\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"source\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>{{ source }}</h3>\n            </div>\n            <div class=\"source-actions\">\n              <i class=\"el-icon-delete\" @click=\"removeCustomSource(index)\"></i>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n\n\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [], // 已选择的关键词\n      maxKeywords: 5, // 最大选择数量\n      selectedDataSources: ['online-search'], // 已选择的数据来源\n      customDataSources: [], // 自定义数据源列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      showValidation: false // 是否显示验证错误样式\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    },\n\n    // 将关键词按分类分组\n    groupedKeywords() {\n      if (this.selectedKeywords.length === 0) {\n        return {}\n      }\n\n      const grouped = {}\n\n      this.selectedKeywords.forEach(keyword => {\n        let category = '其他'\n\n        if (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服')) {\n          category = '售后服务问题'\n        } else if (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障')) {\n          category = '产品质量问题'\n        } else if (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解')) {\n          category = '投诉处理结果'\n        } else if (keyword.includes('不满') || keyword.includes('消费者')) {\n          category = '消费者不满'\n        } else if (keyword.includes('宣传') || keyword.includes('充好')) {\n          category = '虚假宣传'\n        }\n\n        if (!grouped[category]) {\n          grouped[category] = []\n        }\n        grouped[category].push(keyword)\n      })\n\n      return grouped\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 切换数据来源选择\n    toggleDataSource(source) {\n      const index = this.selectedDataSources.indexOf(source)\n      if (index > -1) {\n        this.selectedDataSources.splice(index, 1)\n      } else {\n        this.selectedDataSources.push(source)\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 将新的数据源添加到自定义数据源列表中\n      this.customDataSources.push(trimmedUrl)\n      // 自动选中新添加的数据源\n      this.selectedDataSources.push(trimmedUrl)\n\n      this.$message.success('数据源添加成功')\n      // 清空输入框，但保持表单显示，允许继续添加\n      this.newSourceUrl = ''\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      const sourceToRemove = this.customDataSources[index]\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n      // 从已选择列表中移除\n      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)\n      if (selectedIndex > -1) {\n        this.selectedDataSources.splice(selectedIndex, 1)\n      }\n      this.$message.success('数据源删除成功')\n    },\n\n    // 生成关联词\n    generateRelatedWords() {\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      // 这里可以调用API生成关联词\n      this.$message.info('正在生成关联词...')\n\n      // 模拟生成关联词的过程\n      setTimeout(() => {\n        // 根据实体关键词生成相关的关联词\n        const generatedWords = [\n          '老板电器 售后服务',\n          '老板电器 三包义务',\n          '老板电器 客服态度',\n          '老板电器 质量',\n          '老板电器 燃气灶爆炸',\n          '老板电器 抽油烟机故障',\n          '老板电器 投诉处理',\n          '老板电器 对解',\n          '老板电器 投诉公示',\n          '老板电器 消费者不满',\n          '老板电器 不满',\n          '老板电器 投诉平台',\n          '老板电器 虚假宣传',\n          '老板电器 以次充好'\n        ]\n\n        // 清空之前的关键词，添加新生成的关联词\n        this.selectedKeywords = []\n        generatedWords.forEach(word => {\n          if (this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(word)\n          }\n        })\n\n        this.$message.success('关联词生成成功')\n      }, 1000)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 0;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: center;\n  gap: 60px;\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n\n    .keyword-count {\n      color: #999;\n      font-weight: normal;\n      margin-left: 8px;\n      font-size: 13px;\n\n      &.max-reached {\n        color: #ff4d4f;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-textarea__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-bottom: 16px;\n\n    .section-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .word-count {\n      font-size: 14px;\n      color: #999;\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词文本框包装器\n.keywords-textbox-wrapper {\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  padding: 16px;\n  background: #fff;\n  min-height: 120px;\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    border-color: #5470c6;\n  }\n\n  &:focus-within {\n    border-color: #5470c6;\n    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n  }\n}\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    gap: 30px;\n    padding: 16px 0;\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;iCAiLA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MAAA;MACAC,aAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA;MACA,UAAAV,aAAA,CAAAW,IAAA;QACA;MACA;;MAEA;MACA,UAAAV,mBAAA,CAAAU,IAAA;QACA;MACA;;MAEA;MACA,SAAAT,gBAAA,CAAAU,MAAA;QACA;MACA;MAEA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA;MACA,SAAAX,gBAAA,CAAAU,MAAA;QACA;MACA;MAEA,IAAAE,OAAA;MAEA,KAAAZ,gBAAA,CAAAa,OAAA,WAAAC,OAAA;QACA,IAAAC,QAAA;QAEA,IAAAD,OAAA,CAAAE,QAAA,UAAAF,OAAA,CAAAE,QAAA,UAAAF,OAAA,CAAAE,QAAA;UACAD,QAAA;QACA,WAAAD,OAAA,CAAAE,QAAA,UAAAF,OAAA,CAAAE,QAAA,UAAAF,OAAA,CAAAE,QAAA;UACAD,QAAA;QACA,WAAAD,OAAA,CAAAE,QAAA,UAAAF,OAAA,CAAAE,QAAA,UAAAF,OAAA,CAAAE,QAAA;UACAD,QAAA;QACA,WAAAD,OAAA,CAAAE,QAAA,UAAAF,OAAA,CAAAE,QAAA;UACAD,QAAA;QACA,WAAAD,OAAA,CAAAE,QAAA,UAAAF,OAAA,CAAAE,QAAA;UACAD,QAAA;QACA;QAEA,KAAAH,OAAA,CAAAG,QAAA;UACAH,OAAA,CAAAG,QAAA;QACA;QACAH,OAAA,CAAAG,QAAA,EAAAE,IAAA,CAAAH,OAAA;MACA;MAEA,OAAAF,OAAA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA;IACAC,OAAA,CAAAC,GAAA;EACA;EACAC,OAAA;IACA;IACAC,aAAA,WAAAA,cAAAR,OAAA;MACA,IAAAS,KAAA,QAAAvB,gBAAA,CAAAwB,OAAA,CAAAV,OAAA;MACA,IAAAS,KAAA;QACA;QACA,KAAAvB,gBAAA,CAAAyB,MAAA,CAAAF,KAAA;MACA;QACA;QACA,SAAAvB,gBAAA,CAAAU,MAAA,QAAAT,WAAA;UACA,KAAAD,gBAAA,CAAAiB,IAAA,CAAAH,OAAA;QACA;UACA,KAAAY,QAAA,CAAAC,OAAA,wCAAAC,MAAA,MAAA3B,WAAA;QACA;MACA;IACA;IAEA;IACA4B,iBAAA,WAAAA,kBAAAf,OAAA;MACA,YAAAd,gBAAA,CAAAgB,QAAA,CAAAF,OAAA;IACA;IAGA;IACAgB,YAAA,WAAAA,aAAA;MACA;MACA,KAAAxB,cAAA;;MAEA;MACA,UAAAR,aAAA,CAAAW,IAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,UAAA5B,mBAAA,CAAAU,IAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,SAAA3B,gBAAA,CAAAU,MAAA;QACA,KAAAgB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAArB,cAAA;MACA,SAAAT,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACAkC,gBAAA,WAAAA,iBAAA;MACA,SAAAlC,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACAmC,gBAAA,WAAAA,iBAAAC,MAAA;MACA,IAAAV,KAAA,QAAArB,mBAAA,CAAAsB,OAAA,CAAAS,MAAA;MACA,IAAAV,KAAA;QACA,KAAArB,mBAAA,CAAAuB,MAAA,CAAAF,KAAA;MACA;QACA,KAAArB,mBAAA,CAAAe,IAAA,CAAAgB,MAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAA9B,kBAAA;MACA,KAAAC,YAAA;IACA;IAEA;IACA8B,iBAAA,WAAAA,kBAAA;MACA,KAAA/B,kBAAA;MACA,KAAAC,YAAA;IACA;IAEA;IACA+B,gBAAA,WAAAA,iBAAA;MACA,UAAA/B,YAAA,CAAAI,IAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAU,UAAA;MACA,KAAAA,UAAA,CAAAC,IAAA,MAAAjC,YAAA,CAAAI,IAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAY,UAAA,QAAAlC,YAAA,CAAAI,IAAA;MACA,SAAAN,iBAAA,CAAAa,QAAA,CAAAuB,UAAA;QACA,KAAAb,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAxB,iBAAA,CAAAc,IAAA,CAAAsB,UAAA;MACA;MACA,KAAArC,mBAAA,CAAAe,IAAA,CAAAsB,UAAA;MAEA,KAAAb,QAAA,CAAAc,OAAA;MACA;MACA,KAAAnC,YAAA;IACA;IAEA;IACAoC,kBAAA,WAAAA,mBAAAlB,KAAA;MACA,IAAAmB,cAAA,QAAAvC,iBAAA,CAAAoB,KAAA;MACA;MACA,KAAApB,iBAAA,CAAAsB,MAAA,CAAAF,KAAA;MACA;MACA,IAAAoB,aAAA,QAAAzC,mBAAA,CAAAsB,OAAA,CAAAkB,cAAA;MACA,IAAAC,aAAA;QACA,KAAAzC,mBAAA,CAAAuB,MAAA,CAAAkB,aAAA;MACA;MACA,KAAAjB,QAAA,CAAAc,OAAA;IACA;IAEA;IACAI,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,KAAA;MACA;MACA,UAAA/C,aAAA,CAAAW,IAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,UAAA5B,mBAAA,CAAAU,IAAA;QACA,KAAAiB,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAD,QAAA,CAAAoB,IAAA;;MAEA;MACAC,UAAA;QACA;QACA,IAAAC,cAAA,IACA,aACA,aACA,aACA,WACA,cACA,eACA,aACA,WACA,aACA,cACA,WACA,aACA,aACA,YACA;;QAEA;QACAH,KAAA,CAAA7C,gBAAA;QACAgD,cAAA,CAAAnC,OAAA,WAAAoC,IAAA;UACA,IAAAJ,KAAA,CAAA7C,gBAAA,CAAAU,MAAA,GAAAmC,KAAA,CAAA5C,WAAA;YACA4C,KAAA,CAAA7C,gBAAA,CAAAiB,IAAA,CAAAgC,IAAA;UACA;QACA;QAEAJ,KAAA,CAAAnB,QAAA,CAAAc,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}