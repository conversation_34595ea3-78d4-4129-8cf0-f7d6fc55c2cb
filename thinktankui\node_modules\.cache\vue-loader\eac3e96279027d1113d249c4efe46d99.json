{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=style&index=0&id=040a21b8&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1751527395861}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750933728705}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750933731152}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750933729640}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750933728031}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750933730285}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA0a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file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n        <span class=\"step-number\">1</span>\n        <span class=\"step-text\">舆情分析来源</span>\n      </div>\n      <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n        <span class=\"step-number\">2</span>\n        <span class=\"step-text\">数据概览</span>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <h2 class=\"section-title\">分析需求</h2>\n\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <span class=\"section-label\">选择关联词</span>\n            <span class=\"word-count\">(0/5)</span>\n          </div>\n\n          <div class=\"keywords-textbox-wrapper\">\n            <!-- 显示生成的关键词 -->\n            <div v-if=\"selectedKeywords.length > 0\" class=\"generated-keywords-display\">\n              <div v-for=\"(category, categoryName) in groupedKeywords\" :key=\"categoryName\" class=\"keyword-category\">\n                <div class=\"category-label\">{{ categoryName }}</div>\n                <div class=\"keyword-tags\">\n                  <el-tag\n                    v-for=\"(keyword, index) in category\"\n                    :key=\"index\"\n                    :class=\"['keyword-tag', { selected: isKeywordSelected(keyword) }]\"\n                    @click=\"toggleKeyword(keyword)\"\n                  >\n                    {{ keyword }}\n                  </el-tag>\n                </div>\n              </div>\n            </div>\n\n            <!-- 生成关联词按钮区域 -->\n            <div class=\"words-container\" :class=\"{ 'has-keywords': selectedKeywords.length > 0 }\">\n              <div class=\"generate-word-btn\" @click=\"generateRelatedWords\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>生成关联词</span>\n              </div>\n              <div class=\"word-description\">\n                根据你填写的需求和关键词生成关联词\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <div class=\"source-option\" @click=\"toggleDataSource('online-search')\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"'online-search'\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n            </div>\n          </div>\n\n          <!-- 自定义数据源列表 -->\n          <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-option\">\n            <el-checkbox\n              v-model=\"selectedDataSources\"\n              :value=\"source\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>{{ source }}</h3>\n            </div>\n            <div class=\"source-actions\">\n              <i class=\"el-icon-delete\" @click=\"removeCustomSource(index)\"></i>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" type=\"primary\" size=\"large\">开始分析</el-button>\n      </div>\n    </div>\n\n\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [], // 已选择的关键词\n      maxKeywords: 5, // 最大选择数量\n      selectedDataSources: ['online-search'], // 已选择的数据来源\n      customDataSources: [], // 自定义数据源列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      showValidation: false // 是否显示验证错误样式\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    },\n\n    // 将关键词按分类分组\n    groupedKeywords() {\n      if (this.selectedKeywords.length === 0) {\n        return {}\n      }\n\n      const grouped = {}\n\n      this.selectedKeywords.forEach(keyword => {\n        let category = '其他'\n\n        if (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服')) {\n          category = '售后服务问题'\n        } else if (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障')) {\n          category = '产品质量问题'\n        } else if (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解')) {\n          category = '投诉处理结果'\n        } else if (keyword.includes('不满') || keyword.includes('消费者')) {\n          category = '消费者不满'\n        } else if (keyword.includes('宣传') || keyword.includes('充好')) {\n          category = '虚假宣传'\n        }\n\n        if (!grouped[category]) {\n          grouped[category] = []\n        }\n        grouped[category].push(keyword)\n      })\n\n      return grouped\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('舆情分析页面已加载')\n  },\n  methods: {\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 2) {\n        this.currentStep++\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 切换数据来源选择\n    toggleDataSource(source) {\n      const index = this.selectedDataSources.indexOf(source)\n      if (index > -1) {\n        this.selectedDataSources.splice(index, 1)\n      } else {\n        this.selectedDataSources.push(source)\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 将新的数据源添加到自定义数据源列表中\n      this.customDataSources.push(trimmedUrl)\n      // 自动选中新添加的数据源\n      this.selectedDataSources.push(trimmedUrl)\n\n      this.$message.success('数据源添加成功')\n      // 清空输入框，但保持表单显示，允许继续添加\n      this.newSourceUrl = ''\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      const sourceToRemove = this.customDataSources[index]\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n      // 从已选择列表中移除\n      const selectedIndex = this.selectedDataSources.indexOf(sourceToRemove)\n      if (selectedIndex > -1) {\n        this.selectedDataSources.splice(selectedIndex, 1)\n      }\n      this.$message.success('数据源删除成功')\n    },\n\n    // 生成关联词\n    generateRelatedWords() {\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      // 这里可以调用API生成关联词\n      this.$message.info('正在生成关联词...')\n\n      // 模拟生成关联词的过程\n      setTimeout(() => {\n        // 根据实体关键词生成相关的关联词\n        const generatedWords = [\n          '老板电器 售后服务',\n          '老板电器 三包义务',\n          '老板电器 客服态度',\n          '老板电器 质量',\n          '老板电器 燃气灶爆炸',\n          '老板电器 抽油烟机故障',\n          '老板电器 投诉处理',\n          '老板电器 对解',\n          '老板电器 投诉公示',\n          '老板电器 消费者不满',\n          '老板电器 不满',\n          '老板电器 投诉平台',\n          '老板电器 虚假宣传',\n          '老板电器 以次充好'\n        ]\n\n        // 清空之前的关键词，添加新生成的关联词\n        this.selectedKeywords = []\n        generatedWords.forEach(word => {\n          if (this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(word)\n          }\n        })\n\n        this.$message.success('关联词生成成功')\n      }, 1000)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 0;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: center;\n  gap: 60px;\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n\n    .keyword-count {\n      color: #999;\n      font-weight: normal;\n      margin-left: 8px;\n      font-size: 13px;\n\n      &.max-reached {\n        color: #ff4d4f;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n\n  .requirement-textarea {\n    :deep(.el-textarea__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n      line-height: 1.6;\n      resize: vertical;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-textarea__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-bottom: 16px;\n\n    .section-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .word-count {\n      font-size: 14px;\n      color: #999;\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词文本框包装器\n.keywords-textbox-wrapper {\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  padding: 16px;\n  background: #fff;\n  min-height: 120px;\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    border-color: #5470c6;\n  }\n\n  &:focus-within {\n    border-color: #5470c6;\n    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n  }\n}\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 16px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    gap: 30px;\n    padding: 16px 0;\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n</style>\n"]}]}