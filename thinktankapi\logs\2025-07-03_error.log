2025-07-03 09:29:09.349 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-07-03 09:29:09.349 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-03 11:24:38.082 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-07-03 11:24:38.083 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-03 11:24:39.216 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-03 11:24:39.217 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-03 11:24:39.230 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-03 11:24:39.828 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-03 11:24:40.481 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-03 11:24:40.483 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
2025-07-03 11:25:21.473 | f285ba29bd5b485b8f276de27b7c7f35 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为af6b4c9b-17ef-420d-af8b-68e8ae5f89c3的会话获取图片验证码成功
2025-07-03 11:25:28.193 | 79146a8ff6a34cb0a93021bff4ff748d | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-03 11:25:28.420 | 998437e8813b421eae1f791ff04f474b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 11:25:28.916 | def86463716745ccad5ccba166d50d3a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 11:25:32.966 | 6db0b8e7794248f4876ad86592242504 | INFO     | module_admin.controller.keyword_data_controller:get_web_statistics_public:273 - 获取网站统计数据成功
2025-07-03 11:25:33.252 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-07-03 11:25:33.253 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-07-03 11:25:33.253 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-07-03 11:25:33.253 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-07-03 11:25:33.254 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-07-03 11:25:33.254 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-07-03 11:25:33.254 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-07-03 11:25:33.255 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-07-03 11:25:33.271 | 6f30194d58864ac1bcecdb76ac790869 | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-07-03 11:25:33.313 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-03 11:25:33.314 | f4d496fbfefc4eeb8f459f1fbb43a094 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=1606, title='多重因素压制 棕榈油进入下行周期', content='在尝试采集网页 `http://futures.eastmoney.com/a/202505123401818734.html` 的正文内容时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有其他需求，请随时告诉我！我会尽力帮助您。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:28:08', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1605, title='AI“下沉”千元手机', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505193408529182.html` 的正文时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少请求的限制值。\n3. **稍后重试**：有时候系统可能会有临时的限制，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告诉我！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:27:41', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1604, title='止咳宝片发货量减少致营收下降，特一药业的1亿盒目标何以实现？', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505193408555036.html` 的正文时遇到了问题，具体错误信息为：“Failed to wait for the message endpoint”。\n\n这可能是由于网络连接问题、页面加载超时或其他技术原因导致的。为了确保能够获取到您需要的信息，我们可以尝试以下几种方法：\n\n### 1. **再次尝试采集**\n有时候，简单的重试可以解决临时性的问题。\n\n### 2. **使用不同的采集选项**\n我们可以调整采集参数，例如增加等待时间或选择其他格式进行采集。\n\n### 3. **手动访问网页**\n您可以尝试手动访问该网页，并复制粘贴正文内容。\n\n### 4. **检查网页是否正常访问**\n请确认该网页当前是否可以正常访问，有时网页可能会因为维护或其他原因暂时无法访问。\n\n### 再次尝试采集\n\n我将再次尝试采集该网页的正文内容。\n在尝试采集网页 `http://finance.eastmoney.com/a/202505193408555036.html` 的正文时，遇到了资源限制的问题，具体错误信息为：“Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value。”\n\n### 解决方案\n\n#### 1. **手动访问网页**\n鉴于当前自动采集遇到限制，最直接的方法是手动访问该网页并复制正文内容。\n\n#### 2. **调整采集策略**\n如果后续有更多采集需求，可以考虑以下策略：\n- **降低请求频率**：避免短时间内频繁请求。\n- **优化采集计划**：合理安排采集任务，避开高峰时段。\n- **升级服务计划**：如果经常需要大量采集，可以考虑升级服务计划以获得更多的资源额度。\n\n### 手动访问步骤\n\n1. **打开浏览器**：使用您常用的浏览器。\n2. **访问网页**：在地址栏输入 `http://finance.eastmoney.com/a/202505193408555036.html` 并回车。\n3. **定位正文**：找到页面中的文章正文部分。\n4. **复制内容**：选中正文内容并复制。\n\n### 提供帮助\n\n如果您需要进一步的帮助，例如指导如何手动复制内容或有其他相关问题，请随时告知。同时，如果有其他网页需要采集，也可以提供链接，我们将尽力协助。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:27:12', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1603, title='*ST兰黄首次回购股票约26万股；华致酒行称未来门店发展将主要依托三种业态模式丨酒业早参', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505143403841159.html` 的正文时遇到了问题，具体错误信息为 "Failed to wait for the message endpoint"。这可能是因为页面加载或网络连接的问题。\n\n为了确保能够获取到正确的正文内容，我们可以尝试以下几种方法：\n\n### 方法一：直接访问网页\n你可以直接访问该网页并复制正文内容。\n\n### 方法二：使用不同的工具或方法进行采集\n如果需要我继续尝试其他方式来采集该网页的正文，请告知我，我会尽力提供帮助。\n\n### 方法三：手动输入网址\n如果你有该网页的具体内容或截图，也可以直接提供给我，我可以帮你整理和分析。\n\n请根据上述建议选择合适的方法，或者告诉我是否有其他特定的需求。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:26:12', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1602, title='“网红儿童”成牟利工具？专家呼吁尊重未成年人真实意愿', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505313419495722.html` 的正文内容时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少每次请求的数据量或调整请求参数。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:49', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1601, title='叮咚买菜一季度财报：GMV 59.6亿元 同比增长7.9%', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505163407082510.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **调整请求限制**：如果可能，尝试降低请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:21', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1600, title='发起内部<em>效</em>率革命 汽车巨头掀起整合浪潮', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505243413447388.html` 的正文时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低单次请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告诉我！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:04', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1599, title='河南<em>老板</em>开火锅店年入23亿', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506203436154351.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低单次请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有进一步的需求，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:24:29', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1598, title='年轻人涌入亚朵们 五星级酒店“打骨折”拍卖 煤<em>老板</em>们也不接盘了', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506273441808143.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这表示当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有其他需求，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:24:06', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1597, title='聊城<em>老板</em>卖猫狗粮 暴赚230亿身家', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506263441590083.html` 的正文时遇到了一些问题，具体错误信息为“Failed to wait for the message endpoint”。这可能是因为页面加载或响应出现了延迟。\n\n为了确保能够成功获取到正文内容，我们可以尝试以下几种方法：\n\n### 方法一：增加等待时间\n有时候，网页内容需要更多时间来加载。我们可以通过增加等待时间来解决这个问题。\n\n### 方法二：直接抓取 HTML 内容\n如果上述方法仍然无法解决问题，我们可以尝试直接抓取整个 HTML 页面，然后手动提取正文部分。\n\n### 方法三：检查网络状况\n确保网络连接稳定，有时候网络问题也会导致页面加载失败。\n\n### 尝试方案\n\n现在，我将尝试直接抓取该网页的 HTML 内容，然后再从中提取正文部分。\n在第二次尝试中，我们增加了等待时间和超时时间，但仍然遇到了“Failed to wait for the message endpoint”的问题。这表明页面加载或响应的问题依然存在。\n\n### 备选方案\n\n#### 方案一：使用不同的抓取工具\n我们可以尝试使用其他网页抓取工具或库来获取该网页的内容。\n\n#### 方案二：手动访问网页\n您可以手动访问该网页，并复制正文内容。这是最直接的方法，可以确保获得所需的信息。\n\n#### 方案三：检查网页是否存在\n请确认该网页是否仍然存在且可访问。有时候，网页可能会因为各种原因（如服务器问题、链接失效等）而无法正常加载。\n\n### 当前建议\n\n鉴于当前情况，我建议您手动访问该网页并复制正文内容。这是目前最可靠的方法来获取所需信息。\n\n如果您同意，请提供手动复制的正文内容，我可以帮助您进一步处理或分析。\n\n如果需要进一步的技术支持或其他帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:21:48', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[])] page_num=1 page_size=10 total=1594 has_next=True
2025-07-03 11:29:39.854 | 89e67a8fa26d449c87b7a698949387c3 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 11:29:39.857 | f2eb9de62f774a36a95fc2279c2dd073 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 11:29:40.258 | 3a8f9731773b4903942901082bfad48e | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 11:29:46.417 | d91e5f1e7650436fa9157bf8a6b1cdd3 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:22:19.123 | 3bbc6d054fc64b49bf68803fe57f1cc1 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-03 12:22:20.535 | ad6993d3891d4da98805a75f5fc65662 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-03 12:22:21.387 | 89910994a6a34eafa8eae95ad6971a74 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为6cc21722-05d8-4229-9287-bf2bae584dd7的会话获取图片验证码成功
2025-07-03 12:22:31.479 | 08bcb9f991d840daa649bcc7a7f14272 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-03 12:22:31.708 | 0ecc096ccc404c3ba7a24467fc69a967 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 12:22:32.208 | 30f4d9dfeaa74f839b81f99b1efe5806 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 12:23:34.607 | c42a971aa43746e2a252bb60436d13c3 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 12:23:34.613 | 30adb18a97cf4b9a8f3223a0d6aaef88 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 12:23:34.925 | 03520822d03c45f58b0f0a64db6ad339 | INFO     | module_admin.controller.config_controller:query_system_config:106 - 获取成功
2025-07-03 12:23:34.974 | a04029a914a1409e9af00a98a357be2f | INFO     | module_admin.controller.user_controller:get_system_user_list:68 - 获取成功
2025-07-03 12:23:35.158 | 4b1231e67caf4b55aa1c725b787521f7 | INFO     | module_admin.controller.user_controller:get_system_dept_tree:50 - 获取成功
2025-07-03 12:23:39.607 | 5c060d38c3b8477a831df7cb029c9892 | INFO     | module_admin.controller.role_controller:get_system_role_list:53 - 获取成功
2025-07-03 12:23:41.928 | a9bb5f937a8a4fb18ba60068597cf63f | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 12:23:42.322 | bf55c31f064443e2bbbc7808e2030856 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:23:44.451 | 58f5c02b208441c4994ddab636d59e2e | INFO     | module_admin.controller.dept_controller:get_system_dept_list:50 - 获取成功
2025-07-03 12:23:47.006 | 62cda5d4b72441479235f5afa8d67ac1 | INFO     | module_admin.controller.post_controler:get_system_post_list:32 - 获取成功
2025-07-03 12:23:49.990 | 33d3195595e24d149aeba97f75d9feb3 | INFO     | module_admin.controller.dict_controller:get_system_dict_type_list:42 - 获取成功
2025-07-03 12:24:04.579 | 24cbf930d4ec4e54ba6464f3330425d5 | INFO     | module_generator.controller.gen_controller:get_gen_table_list:33 - 获取成功
2025-07-03 12:24:27.152 | 945445ab323348f4af22891195783bfd | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:25:21.801 | 072cd27b84c742e0a1ade498feae230d | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:27:50.061 | 71581f3dcb06468ea4aeee39e8e39488 | INFO     | module_admin.controller.menu_controller:add_system_menu:75 - 新增成功
2025-07-03 12:27:50.715 | c5eb522d717e4509b6babf87f7ffe29c | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:28:02.817 | 75281efa9f504de2aa550e3871a7acd7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 12:28:03.048 | d6a67437c4964b62b3abc305c483c3c2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 12:28:03.595 | 8cbdd9d98a41466d9aa49d9cbc59bbf3 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 12:28:03.890 | 17c1b6fd68b543e494526416dc70ce66 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 12:28:03.983 | a1810ee45966449d9c5cf4e4102d08db | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:29:29.743 | 92434c954d6a40d3ab975498beb7fd14 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 12:29:29.927 | bc3cf1c6269b4adfb012ffe8c8362804 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 12:29:30.438 | 0a3bb494cebd400ebecdad013f9d1c5d | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 12:29:30.709 | a406d4701dc64d66bd19d8f214a91060 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-07-03 12:29:30.721 | 443f1e46c23f441187099b65b1c8af11 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:29:38.092 | 6db98166199f4d64a0970ee0acbb0476 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:29:38.105 | 662d6af14439435caa6938e3c6172954 | INFO     | module_admin.controller.menu_controller:query_detail_system_menu:112 - 获取menu_id为2000的信息成功
2025-07-03 12:30:03.511 | aa4764980db04483bd0bd2d782e9053b | INFO     | module_admin.controller.menu_controller:edit_system_menu:92 - 更新成功
2025-07-03 12:30:04.312 | fdc832f8dd5a40cea455d328baafd17e | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-07-03 12:30:07.724 | b28672288d5c4572af028ae1c59c126a | INFO     | module_admin.controller.keyword_data_controller:get_web_statistics_public:273 - 获取网站统计数据成功
2025-07-03 12:30:08.012 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-07-03 12:30:08.012 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-07-03 12:30:08.013 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-07-03 12:30:08.013 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-07-03 12:30:08.014 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-07-03 12:30:08.014 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-07-03 12:30:08.014 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-07-03 12:30:08.014 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-07-03 12:30:08.088 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-03 12:30:08.089 | 677606f1a0284f88b8cdc82061407584 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=1606, title='多重因素压制 棕榈油进入下行周期', content='在尝试采集网页 `http://futures.eastmoney.com/a/202505123401818734.html` 的正文内容时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有其他需求，请随时告诉我！我会尽力帮助您。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:28:08', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1605, title='AI“下沉”千元手机', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505193408529182.html` 的正文时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少请求的限制值。\n3. **稍后重试**：有时候系统可能会有临时的限制，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告诉我！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:27:41', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1604, title='止咳宝片发货量减少致营收下降，特一药业的1亿盒目标何以实现？', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505193408555036.html` 的正文时遇到了问题，具体错误信息为：“Failed to wait for the message endpoint”。\n\n这可能是由于网络连接问题、页面加载超时或其他技术原因导致的。为了确保能够获取到您需要的信息，我们可以尝试以下几种方法：\n\n### 1. **再次尝试采集**\n有时候，简单的重试可以解决临时性的问题。\n\n### 2. **使用不同的采集选项**\n我们可以调整采集参数，例如增加等待时间或选择其他格式进行采集。\n\n### 3. **手动访问网页**\n您可以尝试手动访问该网页，并复制粘贴正文内容。\n\n### 4. **检查网页是否正常访问**\n请确认该网页当前是否可以正常访问，有时网页可能会因为维护或其他原因暂时无法访问。\n\n### 再次尝试采集\n\n我将再次尝试采集该网页的正文内容。\n在尝试采集网页 `http://finance.eastmoney.com/a/202505193408555036.html` 的正文时，遇到了资源限制的问题，具体错误信息为：“Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value。”\n\n### 解决方案\n\n#### 1. **手动访问网页**\n鉴于当前自动采集遇到限制，最直接的方法是手动访问该网页并复制正文内容。\n\n#### 2. **调整采集策略**\n如果后续有更多采集需求，可以考虑以下策略：\n- **降低请求频率**：避免短时间内频繁请求。\n- **优化采集计划**：合理安排采集任务，避开高峰时段。\n- **升级服务计划**：如果经常需要大量采集，可以考虑升级服务计划以获得更多的资源额度。\n\n### 手动访问步骤\n\n1. **打开浏览器**：使用您常用的浏览器。\n2. **访问网页**：在地址栏输入 `http://finance.eastmoney.com/a/202505193408555036.html` 并回车。\n3. **定位正文**：找到页面中的文章正文部分。\n4. **复制内容**：选中正文内容并复制。\n\n### 提供帮助\n\n如果您需要进一步的帮助，例如指导如何手动复制内容或有其他相关问题，请随时告知。同时，如果有其他网页需要采集，也可以提供链接，我们将尽力协助。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:27:12', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1603, title='*ST兰黄首次回购股票约26万股；华致酒行称未来门店发展将主要依托三种业态模式丨酒业早参', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505143403841159.html` 的正文时遇到了问题，具体错误信息为 "Failed to wait for the message endpoint"。这可能是因为页面加载或网络连接的问题。\n\n为了确保能够获取到正确的正文内容，我们可以尝试以下几种方法：\n\n### 方法一：直接访问网页\n你可以直接访问该网页并复制正文内容。\n\n### 方法二：使用不同的工具或方法进行采集\n如果需要我继续尝试其他方式来采集该网页的正文，请告知我，我会尽力提供帮助。\n\n### 方法三：手动输入网址\n如果你有该网页的具体内容或截图，也可以直接提供给我，我可以帮你整理和分析。\n\n请根据上述建议选择合适的方法，或者告诉我是否有其他特定的需求。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:26:12', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1602, title='“网红儿童”成牟利工具？专家呼吁尊重未成年人真实意愿', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505313419495722.html` 的正文内容时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少每次请求的数据量或调整请求参数。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:49', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1601, title='叮咚买菜一季度财报：GMV 59.6亿元 同比增长7.9%', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505163407082510.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **调整请求限制**：如果可能，尝试降低请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:21', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1600, title='发起内部<em>效</em>率革命 汽车巨头掀起整合浪潮', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505243413447388.html` 的正文时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低单次请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告诉我！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:04', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1599, title='河南<em>老板</em>开火锅店年入23亿', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506203436154351.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低单次请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有进一步的需求，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:24:29', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1598, title='年轻人涌入亚朵们 五星级酒店“打骨折”拍卖 煤<em>老板</em>们也不接盘了', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506273441808143.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这表示当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有其他需求，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:24:06', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1597, title='聊城<em>老板</em>卖猫狗粮 暴赚230亿身家', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506263441590083.html` 的正文时遇到了一些问题，具体错误信息为“Failed to wait for the message endpoint”。这可能是因为页面加载或响应出现了延迟。\n\n为了确保能够成功获取到正文内容，我们可以尝试以下几种方法：\n\n### 方法一：增加等待时间\n有时候，网页内容需要更多时间来加载。我们可以通过增加等待时间来解决这个问题。\n\n### 方法二：直接抓取 HTML 内容\n如果上述方法仍然无法解决问题，我们可以尝试直接抓取整个 HTML 页面，然后手动提取正文部分。\n\n### 方法三：检查网络状况\n确保网络连接稳定，有时候网络问题也会导致页面加载失败。\n\n### 尝试方案\n\n现在，我将尝试直接抓取该网页的 HTML 内容，然后再从中提取正文部分。\n在第二次尝试中，我们增加了等待时间和超时时间，但仍然遇到了“Failed to wait for the message endpoint”的问题。这表明页面加载或响应的问题依然存在。\n\n### 备选方案\n\n#### 方案一：使用不同的抓取工具\n我们可以尝试使用其他网页抓取工具或库来获取该网页的内容。\n\n#### 方案二：手动访问网页\n您可以手动访问该网页，并复制正文内容。这是最直接的方法，可以确保获得所需的信息。\n\n#### 方案三：检查网页是否存在\n请确认该网页是否仍然存在且可访问。有时候，网页可能会因为各种原因（如服务器问题、链接失效等）而无法正常加载。\n\n### 当前建议\n\n鉴于当前情况，我建议您手动访问该网页并复制正文内容。这是目前最可靠的方法来获取所需信息。\n\n如果您同意，请提供手动复制的正文内容，我可以帮助您进一步处理或分析。\n\n如果需要进一步的技术支持或其他帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:21:48', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[])] page_num=1 page_size=10 total=1594 has_next=True
2025-07-03 12:30:08.246 | 54c5e6763469440880ba649fc8e2efbb | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-07-03 12:30:09.873 | 53f5b48bf60c4052acd05a9d81b1c747 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 12:30:10.106 | a0dff64490f14e6dbe5a80aefd743edb | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 12:30:10.493 | 3d74122edec048ffb4f9b0dc34267c2d | INFO     | module_admin.controller.keyword_data_controller:get_web_statistics_public:273 - 获取网站统计数据成功
2025-07-03 12:30:10.778 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:174 - 查询参数: pageNum=1, pageSize=10, timeFilter=None, actual_start_date=None, actual_end_date=None
2025-07-03 12:30:10.778 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:40 - Service层接收到的查询参数: begin_time=None, end_time=None, time_filter=None
2025-07-03 12:30:10.778 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:41 - Service层查询对象类型: <class 'module_admin.entity.vo.keyword_data_vo.KeywordDataPageQueryModel'>
2025-07-03 12:30:10.778 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.service.keyword_data_service:get_keyword_data_list_services:42 - Service层查询对象属性: {'page_num': 1, 'page_size': 10, 'title': None, 'keyword': None, 'type': None, 'web': None, 'begin_time': None, 'end_time': None, 'platform_types': None, 'sentiment_types': None, 'info_attributes': None, 'web_site': None, 'time_filter': None}
2025-07-03 12:30:10.779 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:112 - DAO层时间筛选参数: begin_time=None, end_time=None, time_filter=None
2025-07-03 12:30:10.779 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:182 - 总共构建了 0 个查询条件
2025-07-03 12:30:10.780 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:183 - 查询参数: time_filter=None, platform_types=None, sentiment_types=None
2025-07-03 12:30:10.780 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.dao.keyword_data_dao:get_keyword_data_list:189 - 没有查询条件，将返回所有数据
2025-07-03 12:30:10.851 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:178 - 查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-03 12:30:10.851 | 1ae9ca05bc294d64b5f0535e6f9c34a1 | INFO     | module_admin.controller.keyword_data_controller:get_keyword_data_list_public:179 - 查询结果: records=[KeywordDataResponseModel(id=1606, title='多重因素压制 棕榈油进入下行周期', content='在尝试采集网页 `http://futures.eastmoney.com/a/202505123401818734.html` 的正文内容时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有其他需求，请随时告诉我！我会尽力帮助您。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:28:08', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1605, title='AI“下沉”千元手机', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505193408529182.html` 的正文时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少请求的限制值。\n3. **稍后重试**：有时候系统可能会有临时的限制，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告诉我！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:27:41', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1604, title='止咳宝片发货量减少致营收下降，特一药业的1亿盒目标何以实现？', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505193408555036.html` 的正文时遇到了问题，具体错误信息为：“Failed to wait for the message endpoint”。\n\n这可能是由于网络连接问题、页面加载超时或其他技术原因导致的。为了确保能够获取到您需要的信息，我们可以尝试以下几种方法：\n\n### 1. **再次尝试采集**\n有时候，简单的重试可以解决临时性的问题。\n\n### 2. **使用不同的采集选项**\n我们可以调整采集参数，例如增加等待时间或选择其他格式进行采集。\n\n### 3. **手动访问网页**\n您可以尝试手动访问该网页，并复制粘贴正文内容。\n\n### 4. **检查网页是否正常访问**\n请确认该网页当前是否可以正常访问，有时网页可能会因为维护或其他原因暂时无法访问。\n\n### 再次尝试采集\n\n我将再次尝试采集该网页的正文内容。\n在尝试采集网页 `http://finance.eastmoney.com/a/202505193408555036.html` 的正文时，遇到了资源限制的问题，具体错误信息为：“Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value。”\n\n### 解决方案\n\n#### 1. **手动访问网页**\n鉴于当前自动采集遇到限制，最直接的方法是手动访问该网页并复制正文内容。\n\n#### 2. **调整采集策略**\n如果后续有更多采集需求，可以考虑以下策略：\n- **降低请求频率**：避免短时间内频繁请求。\n- **优化采集计划**：合理安排采集任务，避开高峰时段。\n- **升级服务计划**：如果经常需要大量采集，可以考虑升级服务计划以获得更多的资源额度。\n\n### 手动访问步骤\n\n1. **打开浏览器**：使用您常用的浏览器。\n2. **访问网页**：在地址栏输入 `http://finance.eastmoney.com/a/202505193408555036.html` 并回车。\n3. **定位正文**：找到页面中的文章正文部分。\n4. **复制内容**：选中正文内容并复制。\n\n### 提供帮助\n\n如果您需要进一步的帮助，例如指导如何手动复制内容或有其他相关问题，请随时告知。同时，如果有其他网页需要采集，也可以提供链接，我们将尽力协助。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:27:12', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1603, title='*ST兰黄首次回购股票约26万股；华致酒行称未来门店发展将主要依托三种业态模式丨酒业早参', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505143403841159.html` 的正文时遇到了问题，具体错误信息为 "Failed to wait for the message endpoint"。这可能是因为页面加载或网络连接的问题。\n\n为了确保能够获取到正确的正文内容，我们可以尝试以下几种方法：\n\n### 方法一：直接访问网页\n你可以直接访问该网页并复制正文内容。\n\n### 方法二：使用不同的工具或方法进行采集\n如果需要我继续尝试其他方式来采集该网页的正文，请告知我，我会尽力提供帮助。\n\n### 方法三：手动输入网址\n如果你有该网页的具体内容或截图，也可以直接提供给我，我可以帮你整理和分析。\n\n请根据上述建议选择合适的方法，或者告诉我是否有其他特定的需求。', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:26:12', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1602, title='“网红儿童”成牟利工具？专家呼吁尊重未成年人真实意愿', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505313419495722.html` 的正文内容时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **降低请求限制**：如果可能，尝试减少每次请求的数据量或调整请求参数。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:49', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1601, title='叮咚买菜一季度财报：GMV 59.6亿元 同比增长7.9%', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505163407082510.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **调整请求限制**：如果可能，尝试降低请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:21', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1600, title='发起内部<em>效</em>率革命 汽车巨头掀起整合浪潮', content='在尝试采集网页 `http://finance.eastmoney.com/a/202505243413447388.html` 的正文时，遇到了一些问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低单次请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或者需要进一步的帮助，请随时告诉我！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:25:04', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1599, title='河南<em>老板</em>开火锅店年入23亿', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506203436154351.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这意味着当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低单次请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有进一步的需求，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:24:29', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1598, title='年轻人涌入亚朵们 五星级酒店“打骨折”拍卖 煤<em>老板</em>们也不接盘了', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506273441808143.html` 的正文时遇到了问题。具体错误信息如下：\n\n> Failed to scrape URL. Status code: 402. Error: Insufficient credits to perform this request. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing or try changing the request limit to a lower value.\n\n这表示当前的请求因信用额度不足而无法完成。为了获取该网页的正文内容，您可以考虑以下几种解决方案：\n\n1. **升级计划**：访问 [https://firecrawl.dev/pricing](https://firecrawl.dev/pricing) 升级您的计划以获得更多的信用额度。\n2. **减少请求限制**：如果可能，尝试降低请求的限制值。\n3. **稍后重试**：有时候系统资源紧张可能导致暂时的信用额度不足，稍后再试可能会解决问题。\n\n如果您有其他网页需要采集或有其他需求，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:24:06', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[]), KeywordDataResponseModel(id=1597, title='聊城<em>老板</em>卖猫狗粮 暴赚230亿身家', content='在尝试采集网页 `http://finance.eastmoney.com/a/202506263441590083.html` 的正文时遇到了一些问题，具体错误信息为“Failed to wait for the message endpoint”。这可能是因为页面加载或响应出现了延迟。\n\n为了确保能够成功获取到正文内容，我们可以尝试以下几种方法：\n\n### 方法一：增加等待时间\n有时候，网页内容需要更多时间来加载。我们可以通过增加等待时间来解决这个问题。\n\n### 方法二：直接抓取 HTML 内容\n如果上述方法仍然无法解决问题，我们可以尝试直接抓取整个 HTML 页面，然后手动提取正文部分。\n\n### 方法三：检查网络状况\n确保网络连接稳定，有时候网络问题也会导致页面加载失败。\n\n### 尝试方案\n\n现在，我将尝试直接抓取该网页的 HTML 内容，然后再从中提取正文部分。\n在第二次尝试中，我们增加了等待时间和超时时间，但仍然遇到了“Failed to wait for the message endpoint”的问题。这表明页面加载或响应的问题依然存在。\n\n### 备选方案\n\n#### 方案一：使用不同的抓取工具\n我们可以尝试使用其他网页抓取工具或库来获取该网页的内容。\n\n#### 方案二：手动访问网页\n您可以手动访问该网页，并复制正文内容。这是最直接的方法，可以确保获得所需的信息。\n\n#### 方案三：检查网页是否存在\n请确认该网页是否仍然存在且可访问。有时候，网页可能会因为各种原因（如服务器问题、链接失效等）而无法正常加载。\n\n### 当前建议\n\n鉴于当前情况，我建议您手动访问该网页并复制正文内容。这是目前最可靠的方法来获取所需信息。\n\n如果您同意，请提供手动复制的正文内容，我可以帮助您进一步处理或分析。\n\n如果需要进一步的技术支持或其他帮助，请随时告知！', source_url=None, keywords='老板效果差', source='组合', time='2025-06-30 15:21:48', platform_name=None, selected=False, sentiment='neutral', views=0, comments=0, images=[])] page_num=1 page_size=10 total=1594 has_next=True
2025-07-03 12:30:10.929 | 7d9c8afb7fd6461cb349585623627bda | INFO     | module_admin.controller.keyword_data_controller:get_filter_statistics_public:256 - 获取筛选统计数据成功
2025-07-03 12:30:14.417 | c9b9be5f6dbe491da0b162c61fefa644 | INFO     | module_admin.controller.api_mapping_controller:get_simple_source_personnel_list:1759 - 获取简化信源人员列表成功
2025-07-03 12:30:14.721 | bb2bd89b8b864c98b14428ef8f506c85 | INFO     | module_admin.controller.api_mapping_controller:get_monitoring_statistics:1681 - 获取监控统计数据成功
2025-07-03 12:30:16.127 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:105 - 开始获取分析概览数据，方案ID: 1
2025-07-03 12:30:16.127 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:109 - 正在获取方案统计数据，方案ID: 1
2025-07-03 12:30:16.163 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:113 - 方案统计数据获取完成，总数: 0, 今日数: 0
2025-07-03 12:30:16.163 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:116 - 正在获取情感统计数据，方案ID: 1
2025-07-03 12:30:16.201 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:121 - 情感统计数据获取完成，数据条数: 0
2025-07-03 12:30:16.201 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:140 - 情感统计数据转换完成，统计项数: 0
2025-07-03 12:30:16.202 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:143 - 正在获取平台统计数据，方案ID: 1
2025-07-03 12:30:16.240 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:148 - 平台统计数据获取完成，数据条数: 0
2025-07-03 12:30:16.241 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:167 - 平台统计数据转换完成，统计项数: 0
2025-07-03 12:30:16.241 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:170 - 正在获取趋势数据，方案ID: 1
2025-07-03 12:30:16.242 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:173 - 趋势数据获取完成，数据条数: 0
2025-07-03 12:30:16.243 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:181 - 使用模拟趋势数据，数据条数: 7
2025-07-03 12:30:16.243 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.service.analysis_service:get_analysis_overview_services:198 - 分析概览数据创建成功，方案ID: 1
2025-07-03 12:30:16.243 | e3009730b798472f89c3b4f54ce4d15d | INFO     | module_admin.controller.emotion_analysis_controller:get_opinion_overview_data:24 - 获取舆情总览数据成功，方案ID: 1
2025-07-03 12:30:16.353 | 74c38ccdcf42477f9477144848a19bbf | INFO     | module_admin.controller.report_controller:get_scheme_type_list:116 - 获取方案类型列表成功
2025-07-03 12:30:16.376 | 8afa4cf6a141467cbda5dac99f78fd12 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-07-03 12:30:16.376 | 8afa4cf6a141467cbda5dac99f78fd12 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:139 - 获取报告中心菜单分类成功，共 4 项
2025-07-03 12:30:16.543 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:52 - === 报告中心方案列表请求 ===
2025-07-03 12:30:16.544 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:53 - 用户ID: 1
2025-07-03 12:30:16.544 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:54 - 页码: 1
2025-07-03 12:30:16.544 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:55 - 页大小: 10
2025-07-03 12:30:16.545 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:56 - 模板类型: None
2025-07-03 12:30:16.545 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:57 - 搜索关键词: None
2025-07-03 12:30:16.546 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:58 - 方案类型ID: None
2025-07-03 12:30:16.670 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.service.scheme_service:get_scheme_list_services:32 - 从数据库获取到 10 条方案记录
2025-07-03 12:30:16.700 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.service.scheme_service:get_scheme_list_services:68 - 转换后的方案列表数量: 10, 总数: 16
2025-07-03 12:30:16.701 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.service.scheme_service:get_scheme_list_services:74 - 返回的分页结果: records数量=10, total=16
2025-07-03 12:30:16.701 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:67 - 获取报告中心方案列表成功，共 16 条记录
2025-07-03 12:30:16.702 | 384114bc31ec44be8c47e09b4270190f | INFO     | module_admin.controller.report_controller:get_report_scheme_list:68 - 分页结果类型: <class 'utils.page_util.PageResponseModel'>, 记录数: 10
2025-07-03 12:30:16.722 | 4c9f4b47533a4594a30fbf483a7795d5 | INFO     | module_admin.service.scheme_service:get_scheme_menu_categories_services:388 - 成功创建 4 个菜单分类
2025-07-03 12:30:16.722 | 4c9f4b47533a4594a30fbf483a7795d5 | INFO     | module_admin.controller.report_controller:get_report_menu_categories:139 - 获取报告中心菜单分类成功，共 4 项
2025-07-03 12:35:01.463 | 675c3a0d19fc433096d2c190f11a4462 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 12:35:01.666 | ca8028eea1414099b476e900e13b9c91 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 12:37:08.150 | dd31384067174498a5d1db20aa91314d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 12:37:08.404 | c48c2b202d084b32a3ea7404a32af965 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:30:34.370 | 88246acbca594d9a8c5d231640e381f1 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-03 13:30:34.459 | cfbbf1d27b4d49d0b2103fd2f518b04f | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-03 13:30:34.502 | d935dd7286c94f76bf567a86374038fa | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为684c659a-736e-4322-88c6-763e35fbbf26的会话获取图片验证码成功
2025-07-03 13:30:38.455 | 621edb625ade4bacb356a83e2e74aa48 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-03 13:30:39.372 | cbbc6fda39f047139810f54be57b6bb2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:30:39.655 | 0c6396620a724fd699d56e584ae01341 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:31:31.822 | 4ee95cc7117c46ef9c2f7cd386199e26 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:31:32.058 | 0876c7fe5188462a96d654aa288d423c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:36:23.283 | 3293296c5e7d401d9a49960c1bdfa8bb | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:36:23.565 | 8684e446b02d4059b9854f16bb8fcaa7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:38:27.633 | 1c47198fe4c14893ba77fbe5f3e5ad82 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:38:27.857 | 5a4d4c2d11b24d60ad506ce4afe3ac5e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:38:47.874 | 2f9c4fcd0ace4a9caf86c38a8236f8c1 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:38:48.148 | 9d20f9425a6f43889f0fde6ff90e3dd8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:39:10.539 | 2318cba0e74548e991f58a3965375678 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:39:10.757 | ea916fdfd32944b2b021f4aec5e16aed | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:41:43.345 | 65624d5c9f7541ffae24573bd72e3149 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:41:43.642 | 14fbc995614b459396d67157840d6d8e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:41:52.991 | f8ac4c9e610348738d64ffed31b27705 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:41:53.215 | 9f13a6095e974cdd9fad2d4fbf336935 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:44:54.998 | 460fe01e19e941698dd2d222d1b86da2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:44:55.278 | 7fd1a4c6795543bf924c321e2613f329 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:46:14.845 | 1a3287e3069a488c81d992a400d37d3f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:46:15.070 | 7015ec6f77e74311889324fa550aeed2 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:46:39.915 | 0614703ccec94ddda4953e3759db53f0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:46:40.191 | ab62ca14db3b47b79ef7914b52f55fb6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:49:37.065 | 4d78fcb67e294ff8a48b8e8a96d9904e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:49:39.077 | c604d6f7cd5f4a558d99129f183657a8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:50:33.024 | dbf3b1520b7f46249f1aa8eb3ebc355d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:50:33.317 | 2ed61bea20264370af4058be0af6e39e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:52:28.903 | b5394eccb1bf40c88a2ba833c3ce3f2f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:52:29.177 | 3fd95b0325e14faaba454c9f5fb4ffca | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:54:40.366 | b5fd15c7e85a4b71b8f072d11f84a993 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:54:40.679 | 15b25ad733074c23a885b2df80746a6e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:54:51.262 | 2dbf3d190a7944878fa783afd4c3e32b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:54:51.503 | 95e228f5e6824e5a959bc0c0f5fa13de | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 13:59:41.798 | b4430e8dc7274a5ca77ea28cdfb411ca | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 13:59:42.089 | be83e046e0e2460595e62a5c9af3beb3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:06:32.360 | b94ff26f0d3d42ee8fd31e12a0595dee | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:06:32.585 | 222e2625f14c42e8b286ce1f8c68b327 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:07:54.931 | 43ded7626eb845eab46e3e85468befb2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:07:55.215 | 5a0698738a4541f980e7c69f1a5733e0 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:13:47.424 | 5f4a93588cb94c93bcb4077b07918528 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:13:47.645 | dc4b90348dc1414fb2d190bf0b5f800c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:20:14.642 | 7e710dc58a1840478a83744ae3428d0d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:20:14.947 | 7b559ef7c52d4dc8bdcde472d133f0e4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:21:10.108 | fc434bbd0a34471cb6ec5ddc9d941ff0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:21:10.338 | 60bb48f5c35644cf9db04ec51e2d885a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:25:07.478 | a96e73a808124864b4d9353735c2ad90 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:25:07.760 | 3ee56540a96442af8c4541309fe80421 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:29:32.148 | 59e1496b2ad3480e8d3499251ea43d49 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:29:32.369 | 4442c7117ebd4c37814135326d4a9b52 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:30:12.286 | 11fc2e826aa0499bb8d4ff84f1f3a60c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:30:12.576 | 4dc888ea13c7497fbb88304323e31acc | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:36:30.387 | 854e17c4b8574919972cc493e017c454 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:36:30.831 | e3ec6d0e2c5b4ef9af95a00d31c9afb1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:39:25.197 | 795bda6763574ab88c263d54a0d13e0c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:39:25.698 | 68cc00872c0c4d869e46983589346dc8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:43:40.071 | 7fd970b3d0a44fc2be23df4a6a5a8864 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:43:40.337 | 2eb8d183a4b648e1a3b0d125a0d4ab89 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:44:42.485 | 5b201c529e2f4b75b9863f6afa1785dd | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:44:42.783 | 4b47e37c33f1449e81e7bde736a911bf | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:47:26.909 | 7503f2a828da4576bf57634a07820aac | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:47:27.174 | 9e1d7c1e20744f579aaa46886574e3aa | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:48:44.685 | 5445159cf0414611b3e7b423e8316dc0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:48:44.951 | 07380c85d6364dc9a5956530803951ce | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:49:11.918 | abf134f2feac4db496b53fd8c08731d5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:49:12.169 | 12ab243c65e9487a8a3d613d2eef4ba8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:49:26.358 | 56baf3653dd748779b1affec6a58a39b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:49:26.649 | 827881b1c33647d49bc76f36590d52f8 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:52:09.244 | 4f7549f714e14fb794cfe2f595ac0286 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:52:09.512 | c8112e5c3bde4c9d8427e08ceed06def | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:52:31.129 | bd6a764b2968492fb191e366ab1e7d26 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:52:31.412 | b3a715121bb44dff81c5bac9c402903e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:53:23.910 | 2f828e9232da4a219836420c3c695f77 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:53:24.167 | a2f9cbb43da2429cacb69157cd2b33c3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:55:31.292 | d20be7d8ad3140c1ab5cfcbe2aa6e2c3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:55:31.568 | 7b9e29c0224645819267553c0b082ae6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 14:56:52.842 | 2312abece84a4f70a185473d2aad1375 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 14:56:53.099 | 6009d112a4ad4364952f5f29f4c71489 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 15:02:30.960 | 65fdc1ca9b154f5d9f18dccecb013228 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 15:02:31.229 | 7f0ca6a63ff042faa55104998fb0fb77 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 15:03:03.619 | c911d50e028e452186e047ccd00fd816 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 15:03:03.880 | 8ea1a0b6b90f4ee48d860b05f615902b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 15:06:10.142 | 62fd1651429b4a168d03135bf0c0874c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 15:06:10.421 | 5ff02b5d544a4a728f76a4a356ea85e9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 15:09:33.522 | 5aa995e4d6f54bb0b2d22bed8bb63784 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 15:09:33.769 | 2be66b0b9dcd4488974d02331a1313e1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 15:13:58.761 | 7497b8ee3f8f4e29b0b99966531c40d3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 15:13:59.048 | ce35d3246f7243f189c1d12a88c96aa9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 15:17:59.159 | 1b1e323a7ddf4d7eb030248a6a7d46ca | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-03 15:17:59.408 | 6ff369bcc4494da0a0e72ff71b7b17d9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-03 15:19:14.946 |  | INFO     | server:lifespan:62 - RuoYi-FastAPI开始启动
2025-07-03 15:19:14.947 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-03 15:19:15.978 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-03 15:19:15.978 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-03 15:19:15.979 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-03 15:19:16.514 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-03 15:19:17.050 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-03 15:19:17.051 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI启动成功
