{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\router\\index.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\router\\index.js", "mtime": 1751517297667}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\babel.config.js", "mtime": 1750933247176}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750933728029}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750933729645}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750933728445}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_vueR<PERSON>er", "_layout", "<PERSON><PERSON>", "use", "Router", "constantRoutes", "exports", "path", "component", "Layout", "hidden", "children", "Promise", "resolve", "then", "_interopRequireWildcard2", "default", "redirect", "name", "meta", "title", "icon", "affix", "dynamicRoutes", "permissions", "activeMenu", "routerPush", "prototype", "push", "routerReplace", "replace", "location", "call", "catch", "err", "_default", "mode", "scroll<PERSON>eh<PERSON>or", "y", "routes"], "sources": ["C:/Users/<USER>/Desktop/thin/thinktankui/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport Router from 'vue-router'\r\n\r\nVue.use(Router)\r\n\r\n/* Layout */\r\nimport Layout from '@/layout'\r\n\r\n/**\r\n * Note: 路由配置项\r\n *\r\n * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1\r\n * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\r\n *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面\r\n *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由\r\n *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由\r\n * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\r\n * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题\r\n * query: '{\"id\": 1, \"name\": \"ry\"}' // 访问路由的默认传递参数\r\n * roles: ['admin', 'common']       // 访问路由的角色权限\r\n * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限\r\n * meta : {\r\n    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)\r\n    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字\r\n    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg\r\n    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示\r\n    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。\r\n  }\r\n */\r\n\r\n// 公共路由\r\nexport const constantRoutes = [\r\n  // 系统路由\r\n  {\r\n    path: '/redirect',\r\n    component: Layout,\r\n    hidden: true,\r\n    children: [\r\n      {\r\n        path: '/redirect/:path(.*)',\r\n        component: () => import('@/views/redirect')\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/login',\r\n    component: () => import('@/views/login'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/register',\r\n    component: () => import('@/views/register'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/404',\r\n    component: () => import('@/views/error/404'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/401',\r\n    component: () => import('@/views/error/401'),\r\n    hidden: true\r\n  },\r\n  {\r\n    path: '/user',\r\n    component: Layout,\r\n    hidden: true,\r\n    redirect: 'noredirect',\r\n    children: [\r\n      {\r\n        path: 'profile',\r\n        component: () => import('@/views/system/user/profile/index'),\r\n        name: 'Profile',\r\n        meta: { title: '个人中心', icon: 'user' }\r\n      }\r\n    ]\r\n  },\r\n\r\n  // 主要功能页面（按顺序排列）\r\n  {\r\n    path: '',\r\n    component: Layout,\r\n    redirect: 'index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/dashboard/index'),\r\n        name: 'Index',\r\n        meta: { title: '首页', icon: 'dashboard', affix: true }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/search-results',\r\n    component: Layout,\r\n    hidden: true,\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/search-results/index'),\r\n        name: 'SearchResults',\r\n        meta: { title: '搜索结果', icon: 'search' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/info-summary',\r\n    component: Layout,\r\n    redirect: '/info-summary/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/info-summary/index'),\r\n        name: 'InfoSummary',\r\n        meta: { title: '信息汇总', icon: 'form' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/spread-analysis',\r\n    component: Layout,\r\n    redirect: '/spread-analysis/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/spread-analysis/index'),\r\n        name: 'SpreadAnalysis',\r\n        meta: { title: '传播分析', icon: 'chart' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/warning-center',\r\n    component: Layout,\r\n    redirect: '/warning-center/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/warning-center/index'),\r\n        name: 'WarningCenter',\r\n        meta: { title: '预警中心', icon: 'alert' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/report-center',\r\n    component: Layout,\r\n    redirect: '/report-center/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/report-center/index'),\r\n        name: 'ReportCenter',\r\n        meta: { title: '报告中心', icon: 'report' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/opinion-overview',\r\n    component: Layout,\r\n    redirect: '/opinion-overview/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/opinion-overview/index'),\r\n        name: 'OpinionOverview',\r\n        meta: { title: '舆情总览', icon: 'monitor' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/my-issues',\r\n    component: Layout,\r\n    redirect: '/my-issues/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/my-issues/index'),\r\n        name: 'MyIssues',\r\n        meta: { title: '我的问题', icon: 'question' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/event-analysis',\r\n    component: Layout,\r\n    redirect: '/event-analysis/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/event-analysis/index'),\r\n        name: 'EventAnalysis',\r\n        meta: { title: '事件分析', icon: 'international' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/source-monitoring',\r\n    component: Layout,\r\n    redirect: '/source-monitoring/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/source-monitoring/index'),\r\n        name: 'SourceMonitoring',\r\n        meta: { title: '信源监测', icon: 'monitor' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/hot-events',\r\n    component: Layout,\r\n    redirect: '/hot-events/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/hot-events/index'),\r\n        name: 'HotEvents',\r\n        meta: { title: '热点事件', icon: 'hot' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/meta-search',\r\n    component: Layout,\r\n    redirect: '/meta-search/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/meta-search/index'),\r\n        name: 'MetaSearch',\r\n        meta: { title: '搜索中心', icon: 'search' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/opinion-analysis',\r\n    component: Layout,\r\n    redirect: '/opinion-analysis/index',\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/opinion-analysis/index'),\r\n        name: 'OpinionAnalysis',\r\n        meta: { title: '舆情分析', icon: 'data-analysis' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/account',\r\n    component: Layout,\r\n    redirect: '/account/index',\r\n    meta: { title: '账号相关', icon: 'user' },\r\n    children: [\r\n      {\r\n        path: 'index',\r\n        component: () => import('@/views/account/index'),\r\n        name: 'Account',\r\n        meta: { title: '我的账号', icon: 'user' }\r\n      },\r\n      {\r\n        path: 'user-management',\r\n        component: () => import('@/views/account/user-management'),\r\n        name: 'UserManagement',\r\n        meta: { title: '用户管理', icon: 'peoples' }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\n// 动态路由，基于用户权限动态去加载\r\nexport const dynamicRoutes = [\r\n  {\r\n    path: '/system/user-auth',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:user:edit'],\r\n    children: [\r\n      {\r\n        path: 'role/:userId(\\\\d+)',\r\n        component: () => import('@/views/system/user/authRole'),\r\n        name: 'AuthRole',\r\n        meta: { title: '分配角色', activeMenu: '/system/user' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/system/role-auth',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:role:edit'],\r\n    children: [\r\n      {\r\n        path: 'user/:roleId(\\\\d+)',\r\n        component: () => import('@/views/system/role/authUser'),\r\n        name: 'AuthUser',\r\n        meta: { title: '分配用户', activeMenu: '/system/role' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/system/dict-data',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['system:dict:list'],\r\n    children: [\r\n      {\r\n        path: 'index/:dictId(\\\\d+)',\r\n        component: () => import('@/views/system/dict/data'),\r\n        name: 'Data',\r\n        meta: { title: '字典数据', activeMenu: '/system/dict' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/monitor/job-log',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['monitor:job:list'],\r\n    children: [\r\n      {\r\n        path: 'index/:jobId(\\\\d+)',\r\n        component: () => import('@/views/monitor/job/log'),\r\n        name: 'JobLog',\r\n        meta: { title: '调度日志', activeMenu: '/monitor/job' }\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '/tool/gen-edit',\r\n    component: Layout,\r\n    hidden: true,\r\n    permissions: ['tool:gen:edit'],\r\n    children: [\r\n      {\r\n        path: 'index/:tableId(\\\\d+)',\r\n        component: () => import('@/views/tool/gen/editTable'),\r\n        name: 'GenEdit',\r\n        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }\r\n      }\r\n    ]\r\n  }\r\n]\r\n\r\n// 防止连续点击多次路由报错\r\nlet routerPush = Router.prototype.push;\r\nlet routerReplace = Router.prototype.replace;\r\n// push\r\nRouter.prototype.push = function push(location) {\r\n  return routerPush.call(this, location).catch(err => err)\r\n}\r\n// replace\r\nRouter.prototype.replace = function push(location) {\r\n  return routerReplace.call(this, location).catch(err => err)\r\n}\r\n\r\nexport default new Router({\r\n  mode: 'history', // 去掉url中的#\r\n  scrollBehavior: () => ({ y: 0 }),\r\n  routes: constantRoutes\r\n})\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AAKA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;AAHAG,YAAG,CAACC,GAAG,CAACC,kBAAM,CAAC;;AAEf;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO,IAAMC,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG;AAC5B;AACA;EACEE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;MAAA;IAAA;EAC5C,CAAC;AAEL,CAAC,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,eAAe;IAAA;EAAA,CAAC;EACxCW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,kBAAkB;IAAA;EAAA,CAAC;EAC3CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;MAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mBAAmB;IAAA;EAAA,CAAC;EAC5CW,MAAM,EAAE;AACV,CAAC,EACD;EACEH,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZO,QAAQ,EAAE,YAAY;EACtBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,mCAAmC;MAAA;IAAA,CAAC;IAC5DmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC;AAEL,CAAC;AAED;AACA;EACEd,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,OAAO;EACjBN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDmB,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;MAAEC,KAAK,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAK;EACtD,CAAC;AAEL,CAAC,EACD;EACEf,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EACxC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,qBAAqB;EAC/BN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDmB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,wBAAwB;EAClCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,+BAA+B;MAAA;IAAA,CAAC;IACxDmB,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAQ;EACvC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,uBAAuB;EACjCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAQ;EACvC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,sBAAsB;EAChCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,6BAA6B;MAAA;IAAA,CAAC;IACtDmB,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EACxC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,yBAAyB;EACnCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,gCAAgC;MAAA;IAAA,CAAC;IACzDmB,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU;EACzC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,kBAAkB;EAC5BN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAW;EAC1C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,uBAAuB;EACjCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAgB;EAC/C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,0BAA0B;EACpCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DmB,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU;EACzC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,mBAAmB;EAC7BN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDmB,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM;EACrC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,oBAAoB;EAC9BN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,2BAA2B;MAAA;IAAA,CAAC;IACpDmB,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS;EACxC,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,yBAAyB;EACnCN,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,gCAAgC;MAAA;IAAA,CAAC;IACzDmB,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAgB;EAC/C,CAAC;AAEL,CAAC,EACD;EACEd,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEC,eAAM;EACjBQ,QAAQ,EAAE,gBAAgB;EAC1BE,IAAI,EAAE;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE;EAAO,CAAC;EACrCV,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,OAAO;IACbC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,uBAAuB;MAAA;IAAA,CAAC;IAChDmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAO;EACtC,CAAC,EACD;IACEd,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,iCAAiC;MAAA;IAAA,CAAC;IAC1DmB,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU;EACzC,CAAC;AAEL,CAAC,CACF;;AAED;AACO,IAAME,aAAa,GAAAjB,OAAA,CAAAiB,aAAA,GAAG,CAC3B;EACEhB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,8BAA8B;MAAA;IAAA,CAAC;IACvDmB,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,qBAAqB;IAC3BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,0BAA0B;MAAA;IAAA,CAAC;IACnDmB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,kBAAkB,CAAC;EACjCb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,oBAAoB;IAC1BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,yBAAyB;MAAA;IAAA,CAAC;IAClDmB,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEK,UAAU,EAAE;IAAe;EACpD,CAAC;AAEL,CAAC,EACD;EACElB,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEC,eAAM;EACjBC,MAAM,EAAE,IAAI;EACZc,WAAW,EAAE,CAAC,eAAe,CAAC;EAC9Bb,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,sBAAsB;IAC5BC,SAAS,EAAE,SAAXA,SAASA,CAAA;MAAA,OAAAI,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,WAAAC,wBAAA,CAAAC,OAAA,EAAAjB,OAAA,CAAe,4BAA4B;MAAA;IAAA,CAAC;IACrDmB,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;MAAEC,KAAK,EAAE,QAAQ;MAAEK,UAAU,EAAE;IAAY;EACnD,CAAC;AAEL,CAAC,CACF;;AAED;AACA,IAAIC,UAAU,GAAGtB,kBAAM,CAACuB,SAAS,CAACC,IAAI;AACtC,IAAIC,aAAa,GAAGzB,kBAAM,CAACuB,SAAS,CAACG,OAAO;AAC5C;AACA1B,kBAAM,CAACuB,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACG,QAAQ,EAAE;EAC9C,OAAOL,UAAU,CAACM,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC1D,CAAC;AACD;AACA9B,kBAAM,CAACuB,SAAS,CAACG,OAAO,GAAG,SAASF,IAAIA,CAACG,QAAQ,EAAE;EACjD,OAAOF,aAAa,CAACG,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC7D,CAAC;AAAA,IAAAC,QAAA,GAAA7B,OAAA,CAAAU,OAAA,GAEc,IAAIZ,kBAAM,CAAC;EACxBgC,IAAI,EAAE,SAAS;EAAE;EACjBC,cAAc,EAAE,SAAhBA,cAAcA,CAAA;IAAA,OAAS;MAAEC,CAAC,EAAE;IAAE,CAAC;EAAA,CAAC;EAChCC,MAAM,EAAElC;AACV,CAAC,CAAC", "ignoreList": []}]}